spring:
  application:
    name: email-ai-reader
  
  datasource:
    url: *****************************************************
    username: email_ai_reader
    password: 4DwcB8Q2ncSd8pYh
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    
  mail:
    properties:
      "[mail.smtp.connectiontimeout]": 5000
      "[mail.smtp.timeout]": 3000
      "[mail.smtp.writetimeout]": 5000

  ai:
    alibaba:
      api-key: ${ALIBABA_AI_API_KEY}
      model: qwen-plus

  security:
    oauth2:
      client:
        registration:
          google:
            client-id: ${GOOGLE_CLIENT_ID}
            client-secret: ${GOOGLE_CLIENT_SECRET}
            scope: 
              - email
              - profile
              - https://www.googleapis.com/auth/gmail.readonly
        provider:
          google:
            authorization-uri: https://accounts.google.com/o/oauth2/auth
            token-uri: https://oauth2.googleapis.com/token
            user-info-uri: https://www.googleapis.com/oauth2/v2/userinfo

server:
  port: 8080

app:
  file:
    upload-dir: ${FILE_UPLOAD_DIR:./uploads}
    max-size: 50MB
  email:
    attachment-dir: ${ATTACHMENT_DIR:./attachments}
  n8n:
    webhook-url: http://10.154.177.213:5678/webhook/email-processor
    api-url: http://10.154.177.213:5678/api/v1

logging:
  level:
    com.emailai: DEBUG
    org.springframework.mail: DEBUG
